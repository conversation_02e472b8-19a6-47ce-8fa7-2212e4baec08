-- Migration to add file_size column to images table
-- This column tracks the actual file size of uploaded images

-- Add file_size column to images table
ALTER TABLE images 
ADD COLUMN IF NOT EXISTS file_size BIGINT DEFAULT 0;

-- Create an index on file_size for better query performance
CREATE INDEX IF NOT EXISTS idx_images_file_size ON images(file_size);

-- Update existing images with file_size = 0 (will need to be updated separately)
UPDATE images 
SET file_size = 0 
WHERE file_size IS NULL;

-- Add comment to the column
COMMENT ON COLUMN images.file_size IS 'Size of the image file in bytes';
import { clearCacheByPattern, invalidateTableCache } from '../db/unified';
import logger from '../utils/logger';
import { getIO } from './socketService';

/**
 * Service for coordinating cache invalidation across the application
 */
export class CacheInvalidationService {
  /**
   * Invalidate user statistics cache
   */
  async invalidateUserStats(userId: string) {
    try {
      // Clear cached queries related to user statistics
      clearCacheByPattern(`query:%user_id = $1%${userId}%`);
      clearCacheByPattern(`query:%p.user_id = $1%${userId}%`);
      
      // Emit socket event to notify frontend to refresh
      const io = getIO();
      if (io) {
        io.to(`user:${userId}`).emit('statistics-update-needed');
      }
      
      logger.debug('User statistics cache invalidated', { userId });
    } catch (error) {
      logger.error('Error invalidating user stats cache', { error, userId });
    }
  }

  /**
   * Invalidate cache when images are uploaded
   */
  async onImagesUploaded(userId: string, projectId: string, imageCount: number) {
    try {
      // Invalidate user statistics
      await this.invalidateUserStats(userId);
      
      // Invalidate project-related cache
      clearCacheByPattern(`query:%project_id = $1%${projectId}%`);
      
      // Invalidate images table cache
      invalidateTableCache('images');
      
      logger.info('Cache invalidated after image upload', { 
        userId, 
        projectId, 
        imageCount 
      });
    } catch (error) {
      logger.error('Error invalidating cache after image upload', { error });
    }
  }

  /**
   * Invalidate cache when images are deleted
   */
  async onImagesDeleted(userId: string, projectId: string, imageIds: string[]) {
    try {
      // Invalidate user statistics
      await this.invalidateUserStats(userId);
      
      // Invalidate project cache
      clearCacheByPattern(`query:%project_id = $1%${projectId}%`);
      
      // Invalidate specific image caches
      for (const imageId of imageIds) {
        clearCacheByPattern(`query:%image_id = $1%${imageId}%`);
      }
      
      // Invalidate images table cache
      invalidateTableCache('images');
      
      logger.info('Cache invalidated after image deletion', { 
        userId, 
        projectId, 
        deletedCount: imageIds.length 
      });
    } catch (error) {
      logger.error('Error invalidating cache after image deletion', { error });
    }
  }

  /**
   * Invalidate cache when segmentation is completed
   */
  async onSegmentationCompleted(imageId: string) {
    try {
      // Get user ID from image
      const { query } = await import('../db/unified');
      const result = await query(
        `SELECT p.user_id 
         FROM images i 
         JOIN projects p ON i.project_id = p.id 
         WHERE i.id = $1`,
        [imageId]
      );
      
      if (result.rows.length > 0) {
        const userId = result.rows[0].user_id;
        
        // Invalidate user statistics
        await this.invalidateUserStats(userId);
        
        // Invalidate segmentation-related cache
        clearCacheByPattern(`query:%segmentation%`);
        clearCacheByPattern(`query:%status = 'completed'%`);
      }
      
      // Invalidate specific image cache
      clearCacheByPattern(`query:%image_id = $1%${imageId}%`);
      
      logger.info('Cache invalidated after segmentation completion', { imageId });
    } catch (error) {
      logger.error('Error invalidating cache after segmentation', { error, imageId });
    }
  }

  /**
   * Invalidate cache when projects are created or deleted
   */
  async onProjectChange(userId: string, projectId?: string) {
    try {
      // Invalidate user statistics
      await this.invalidateUserStats(userId);
      
      // Invalidate project table cache
      invalidateTableCache('projects');
      
      if (projectId) {
        clearCacheByPattern(`query:%project_id = $1%${projectId}%`);
      }
      
      logger.info('Cache invalidated after project change', { userId, projectId });
    } catch (error) {
      logger.error('Error invalidating cache after project change', { error });
    }
  }

  /**
   * Clear all caches (for admin use)
   */
  async clearAllCaches() {
    try {
      const { clearAllCache } = await import('../db/unified');
      clearAllCache();
      logger.info('All caches cleared');
    } catch (error) {
      logger.error('Error clearing all caches', { error });
    }
  }
}

export default new CacheInvalidationService();
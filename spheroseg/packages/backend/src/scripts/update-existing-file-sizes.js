const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

const pool = new Pool({
  host: process.env.DB_HOST || 'db',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'spheroseg',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
});

async function updateFileSizes() {
  const client = await pool.connect();
  try {
    console.log('Updating file sizes for existing images...');
    
    // Get all images with file_size = 0
    const result = await client.query(`
      SELECT id, storage_path, metadata 
      FROM images 
      WHERE file_size = 0 OR file_size IS NULL
    `);
    
    console.log(`Found ${result.rows.length} images to update`);
    
    let updatedCount = 0;
    let errorCount = 0;
    
    for (const image of result.rows) {
      try {
        let fileSize = 0;
        
        // First try to get size from metadata if it exists
        if (image.metadata && image.metadata.originalSize) {
          fileSize = image.metadata.originalSize;
        } else if (image.storage_path) {
          // Try to get actual file size from filesystem
          const uploadDir = process.env.UPLOAD_DIR || '/app/uploads';
          const fullPath = path.join(uploadDir, image.storage_path);
          
          if (fs.existsSync(fullPath)) {
            const stats = fs.statSync(fullPath);
            fileSize = stats.size;
          } else {
            // If file doesn't exist, estimate 1MB per image as fallback
            fileSize = 1024 * 1024;
          }
        } else {
          // Default to 1MB if no path available
          fileSize = 1024 * 1024;
        }
        
        // Update the image with the file size
        await client.query(
          'UPDATE images SET file_size = $1 WHERE id = $2',
          [fileSize, image.id]
        );
        
        updatedCount++;
        if (updatedCount % 100 === 0) {
          console.log(`Updated ${updatedCount} images...`);
        }
      } catch (error) {
        console.error(`Error updating image ${image.id}:`, error.message);
        errorCount++;
      }
    }
    
    console.log(`\nUpdate completed!`);
    console.log(`Successfully updated: ${updatedCount} images`);
    console.log(`Errors: ${errorCount} images`);
    
    // Update storage_used_bytes for all users based on their images
    console.log('\nUpdating user storage totals...');
    
    const updateUsersQuery = `
      UPDATE users u
      SET storage_used_bytes = COALESCE((
        SELECT SUM(i.file_size)
        FROM images i
        JOIN projects p ON i.project_id = p.id
        WHERE p.user_id = u.id
      ), 0)
      WHERE EXISTS (
        SELECT 1 FROM projects p WHERE p.user_id = u.id
      )
    `;
    
    await client.query(updateUsersQuery);
    console.log('User storage totals updated successfully!');
    
  } catch (error) {
    console.error('Error updating file sizes:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

updateFileSizes();
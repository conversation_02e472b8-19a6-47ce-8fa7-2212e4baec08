
import express from 'express';
import multer from 'multer';
import sharp from 'sharp';
import { logger } from '../utils/logger';

const router = express.Router();
const upload = multer({ storage: multer.memoryStorage() });

router.post('/generate', upload.single('file'), async (req, res) => {
    if (!req.file) {
        return res.status(400).send('No file uploaded.');
    }

    try {
        const imageBuffer = req.file.buffer;
        const previewBuffer = await sharp(imageBuffer)
            .resize(200)
            .jpeg()
            .toBuffer();
        res.set('Content-Type', 'image/jpeg');
        res.send(previewBuffer);
    } catch (error) {
        logger.error('Error generating preview:', error);
        res.status(500).send('Error generating preview.');
    }
});

export default router;
